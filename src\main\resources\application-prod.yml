# 生产环境配置
logging:
  level:
    root: INFO
    com.newnary.rpa.safebox: INFO
    # 生产环境只记录重要的安全日志
    com.newnary.rpa.safebox.service.SecurityLogService: INFO
    com.newnary.rpa.safebox.service.SecurityManagerService: INFO
    com.newnary.rpa.safebox.service.SecurityApplyService: INFO
    # Spring框架日志保持WARN级别
    org.springframework.web: WARN
    org.springframework.security: WARN
    # 第三方库保持ERROR级别
    org.linguafranca.pwdb: ERROR
    com.larksuite.oapi: ERROR
  file:
    name: ./logs/safebox.log

# 生产环境特定配置
server:
  port: 8080

spring:
  thymeleaf:
    cache: false  # 生产环境模板缓存
  web:
    resources:
      cache:
        period: 0  # 静态资源缓存时间

safebox:
  access-password: uz4Xe
  lark-bot:
    app-id: cli_a83fdf4b39311013
    app-secret: vXM7DDUZy8P9YCcKwbFlQfYG6LDC5Vug
    security-apply-card-id: AAq9wuGPBGj9w
    passwordExpirationCardId: AAq9GK0qiFGWP
    process-receive-id: ou_8254cf6ba62c6eb5bbbd1705d5ec0d91