# 开发环境配置
logging:
  level:
    root: INFO
    com.newnary.rpa.safebox: INFO
    # 开发环境下可以看到更多Spring框架的日志
    org.springframework.web: INFO
    org.springframework.security: INFO
    # HTTP请求详细日志
    org.springframework.web.servlet.DispatcherServlet: INFO
    # 第三方库保持INFO级别
    org.linguafranca.pwdb: INFO
    com.larksuite.oapi: INFO
  file:
    name: ./logs/safebox-dev.log

# 开发环境特定配置
server:
  port: 8080

spring:
  thymeleaf:
    cache: false  # 开发环境禁用模板缓存
  web:
    resources:
      cache:
        period: 0  # 开发环境禁用静态资源缓存

safebox:
  accessPassword: 123456 # 接口访问密码
  lark-bot:
    app-id: cli_a83fdf4b39311013
    app-secret: vXM7DDUZy8P9YCcKwbFlQfYG6LDC5Vug
    security-apply-card-id: AAq9wuGPBGj9w
    passwordExpirationCardId: AAq9GK0qiFGWP
    process-receive-id: ou_8254cf6ba62c6eb5bbbd1705d5ec0d91