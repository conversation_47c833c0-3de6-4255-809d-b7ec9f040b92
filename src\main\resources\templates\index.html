<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>密码管理系统</title>
    <link rel="stylesheet" th:href="@{/css/style.css}">
    <!-- 引入crypto-js库用于客户端签名计算 -->
    <script th:src="@{/js/crypto-js.min.js}"></script>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>密码管理系统</h1>
            <p>基于KeePass的安全密码管理方案</p>
        </div>

        <!-- 导航菜单 -->
        <ul class="nav-menu">
            <li><a href="#" onclick="showPage('homePage')" class="active">首页</a></li>
            <li><a href="#" onclick="showPage('uploadPage')">上传数据库</a></li>
            <li><a href="#" onclick="showPage('metadataPage')">数据库信息</a></li>
            <li><a href="#" onclick="showPage('entriesPage')">查看条目</a></li>
            <li><a href="#" onclick="showPage('larkUsersPage')">飞书用户</a></li>
            <li><a href="#" onclick="showPage('deletePage')">删除数据库</a></li>
            <li><a href="#" onclick="clearAuthPassword()">退出登录</a></li>
        </ul>

        <!-- 内容区域 -->
        <div class="content">
            <!-- 首页 -->
            <div id="homePage" class="page">
                <div class="card">
                    <h2>欢迎使用密码管理系统</h2>
                    <p>这是一个基于KeePass的安全密码管理系统，提供以下功能：</p>
                    <ul style="margin: 20px 0; padding-left: 30px;">
                        <li><strong>上传数据库</strong>：上传您的KeePass数据库文件(.kdbx)</li>
                        <li><strong>数据库信息</strong>：查看数据库的详细元数据信息</li>
                        <li><strong>查看条目</strong>：浏览数据库中的所有密码条目</li>
                        <li><strong>删除数据库</strong>：安全删除已上传的数据库</li>
                    </ul>
                    <p>请使用上方的导航菜单选择您需要的功能。</p>
                </div>
            </div>

            <!-- 上传数据库页面 -->
            <div id="uploadPage" class="page hidden">
                <div class="card">
                    <h2>上传KeePass数据库</h2>
                    <form id="uploadForm" onsubmit="event.preventDefault(); uploadDatabase();">
                        <div class="form-group">
                            <label for="file">选择数据库文件 (.kdbx)</label>
                            <input type="file" id="file" name="file" class="form-control" accept=".kdbx" required>
                        </div>
                        <div class="form-group">
                            <label for="originalPassword">原始主密码</label>
                            <div class="password-input-group">
                                <input type="password" id="originalPassword" name="originalPassword" class="form-control" placeholder="请输入当前数据库的主密码" required>
                                <button type="button" class="password-toggle" onclick="togglePasswordVisibility('originalPassword', 'originalPasswordEye')" title="显示/隐藏密码">
                                    <span id="originalPasswordEye">👁️</span>
                                </button>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="newPassword">新主密码（可选）</label>
                            <div style="display: flex; gap: 10px;">
                                <div class="password-input-group" style="flex: 1;">
                                    <input type="password" id="newPassword" name="newPassword" class="form-control" placeholder="留空则使用原始密码，或输入新密码">
                                    <button type="button" class="password-toggle" onclick="togglePasswordVisibility('newPassword', 'newPasswordEye')" title="显示/隐藏密码">
                                        <span id="newPasswordEye">👁️</span>
                                    </button>
                                </div>
                                <button type="button" class="btn btn-secondary" onclick="generateNewPassword()" title="生成随机密码">生成密码</button>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">上传数据库</button>
                    </form>
                </div>
                <div id="uploadResult"></div>
            </div>

            <!-- 数据库元数据页面 -->
            <div id="metadataPage" class="page hidden">
                <div class="card">
                    <h2>数据库信息</h2>
                    <p>以下是当前数据库的详细信息：</p>
                </div>
                <div id="metadataResult"></div>
            </div>

            <!-- 查看条目页面 -->
            <div id="entriesPage" class="page hidden">
                <div class="card">
                    <h2>查看所有条目</h2>
                    <div class="form-group">
                        <label for="entriesPassword">主密码</label>
                        <input type="password" id="entriesPassword" class="form-control" placeholder="请输入主密码以查看条目">
                    </div>
                    <button type="button" class="btn btn-primary" onclick="loadEntries()">获取条目</button>
                </div>
                <div id="entriesResult"></div>
            </div>

            <!-- 飞书用户页面 -->
            <div id="larkUsersPage" class="page hidden">
                <div class="card">
                    <h2>飞书用户管理</h2>
                    <p>查看已进入机器人单聊的飞书用户列表</p>
                    <button type="button" class="btn btn-primary" onclick="loadLarkUsers()">刷新用户列表</button>
                </div>
                <div id="larkUsersResult"></div>
            </div>

            <!-- 删除数据库页面 -->
            <div id="deletePage" class="page hidden">
                <div class="card">
                    <h2>删除数据库</h2>
                    <div class="alert alert-danger">
                        <strong>警告：</strong>此操作将永久删除数据库文件，无法恢复！请谨慎操作。
                    </div>
                    <div class="form-group">
                        <label for="deletePassword">主密码</label>
                        <input type="password" id="deletePassword" class="form-control" placeholder="请输入主密码以确认删除">
                    </div>
                    <button type="button" class="btn btn-danger" onclick="deleteDatabase()">删除数据库</button>
                </div>
                <div id="deleteResult"></div>
            </div>
        </div>
    </div>

    <!-- 鉴权密码输入模态框 -->
    <div id="authModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>API鉴权验证</h2>
                <p>请输入API访问密码以继续使用系统</p>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="authPassword">API访问密码</label>
                    <div class="password-input-group">
                        <input type="password" id="authPassword" class="form-control" placeholder="请输入API访问密码" required>
                        <button type="button" class="password-toggle" onclick="togglePasswordVisibility('authPassword', 'authPasswordEye')" title="显示/隐藏密码">
                            <span id="authPasswordEye">👁️</span>
                        </button>
                    </div>
                </div>
                <div id="authError" class="alert alert-danger" style="display: none;"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" onclick="submitAuthPassword()">确认</button>
            </div>
        </div>
    </div>

    <script th:src="@{/js/app.js}"></script>
</body>
</html>
