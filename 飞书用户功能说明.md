# 飞书用户查看功能说明

## 功能概述

为 Safebox 密码管理系统的 Web 管理页面新增了飞书用户查看功能，允许管理员查看已进入机器人单聊的飞书用户列表。

## 功能特性

### 1. 导航菜单
- 在主导航菜单中新增"飞书用户"选项
- 位置：在"查看条目"和"删除数据库"之间

### 2. 用户列表展示
- **用户统计**：显示当前用户总数
- **用户信息**：展示用户名和 Open ID
- **表格展示**：清晰的表格格式，包含序号、用户名、Open ID 三列
- **空状态处理**：当没有用户数据时显示友好提示信息

### 3. 交互功能
- **自动加载**：点击"飞书用户"菜单时自动加载用户数据
- **手动刷新**：提供"刷新用户列表"按钮，支持手动更新数据
- **错误处理**：网络错误或接口异常时显示详细错误信息

## 技术实现

### 后端接口
- **接口路径**：`GET /getLarkUsers`
- **返回格式**：`BaseResponse<Map<String, String>>`
- **数据结构**：Map 的 key 为 openId，value 为用户名

### 前端实现
- **页面结构**：在 `index.html` 中新增 `larkUsersPage` 页面
- **JavaScript 函数**：`loadLarkUsers()` 负责数据加载和展示
- **鉴权机制**：使用 `authenticatedFetch()` 确保接口调用安全

## 使用方法

1. **访问功能**
   - 启动 Safebox 应用程序
   - 在浏览器中访问 `http://localhost:8080`
   - 输入 API 访问密码进行鉴权

2. **查看用户列表**
   - 点击导航菜单中的"飞书用户"选项
   - 系统会自动加载并显示用户列表
   - 如需刷新数据，点击"刷新用户列表"按钮

3. **用户数据来源**
   - 用户数据来自飞书机器人的单聊事件
   - 当用户首次进入机器人单聊时，系统会自动获取并缓存用户信息
   - 用户信息存储在 `LarkInteractionService` 的内存缓存中

## 注意事项

- 用户数据仅在应用程序运行期间保存在内存中
- 重启应用程序后用户列表会清空，需要用户重新进入单聊
- 该功能需要飞书机器人正确配置并连接到飞书开放平台
- 用户名显示为"未知用户"表示获取用户信息失败，但 Open ID 仍然有效

## 界面预览

### 有用户数据时
```
飞书用户管理
查看已进入机器人单聊的飞书用户列表
[刷新用户列表]

用户列表 (共 X 个用户)
┌────┬──────────┬─────────────────────────┐
│序号│ 用户名   │ Open ID                 │
├────┼──────────┼─────────────────────────┤
│ 1  │ 张三     │ ou_xxx...               │
│ 2  │ 李四     │ ou_yyy...               │
└────┴──────────┴─────────────────────────┘
```

### 无用户数据时
```
飞书用户管理
查看已进入机器人单聊的飞书用户列表
[刷新用户列表]

暂无用户数据
还没有用户进入机器人单聊，或者用户数据尚未加载。
```
