package com.newnary.rpa.safebox.service;

import com.google.gson.JsonParser;
import com.lark.oapi.Client;
import com.lark.oapi.core.utils.Jsons;
import com.lark.oapi.event.EventDispatcher;
import com.lark.oapi.event.cardcallback.P2CardActionTriggerHandler;
import com.lark.oapi.event.cardcallback.model.CallBackCard;
import com.lark.oapi.event.cardcallback.model.CallBackToast;
import com.lark.oapi.event.cardcallback.model.P2CardActionTrigger;
import com.lark.oapi.event.cardcallback.model.P2CardActionTriggerResponse;
import com.lark.oapi.service.application.ApplicationService;
import com.lark.oapi.service.application.v6.model.P2BotMenuV6;
import com.lark.oapi.service.contact.v3.model.GetUserReq;
import com.lark.oapi.service.contact.v3.model.GetUserResp;
import com.lark.oapi.service.im.ImService;
import com.lark.oapi.service.im.v1.model.CreateMessageReq;
import com.lark.oapi.service.im.v1.model.CreateMessageReqBody;
import com.lark.oapi.service.im.v1.model.CreateMessageResp;
import com.lark.oapi.service.im.v1.model.P2ChatAccessEventBotP2pChatEnteredV1;
import com.lark.oapi.service.im.v1.model.ext.MessageTemplate;
import com.lark.oapi.service.im.v1.model.ext.MessageTemplateData;
import com.newnary.rpa.safebox.common.SecurityApplyCard;
import com.newnary.rpa.safebox.config.SafeboxProperties;
import com.newnary.rpa.safebox.event.SecurityAuthorizeEventEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 飞书交互卡片对接
 *
 * <AUTHOR>
 * @since Created on 2025-09-12
 **/
@Service
@Slf4j
public class LarkInteractionService {

    private final Map<String, String> userMap = new ConcurrentHashMap<>();

    @Resource
    private SafeboxProperties safeboxProperties;
    @Resource
    private ApplicationEventPublisher publisher;

    private Client client;
    private com.lark.oapi.ws.Client wsClient;

    @PostConstruct
    void initialize() {
        synchronized (this) {
            if (client != null) {
                return;
            }
            // 创建 LarkClient 对象
            client = new Client.Builder(safeboxProperties.getLarkBot().getAppId(), safeboxProperties.getLarkBot().getAppSecret()).build();
            // 创建 LarkClient 对象, 并注册事件处理器
            wsClient = new com.lark.oapi.ws.Client.Builder(safeboxProperties.getLarkBot().getAppId(), safeboxProperties.getLarkBot().getAppSecret())
                    .eventHandler(buildEventHandler()).build();
            // 启动
            wsClient.start();
        }
    }

    public Map<String, String> getUserMap() {
        return userMap;
    }

    private EventDispatcher buildEventHandler() {
        return EventDispatcher.newBuilder("", "") // 长连接不需要这两个参数，请保持空字符串
                .onP2ChatAccessEventBotP2pChatEnteredV1(new ImService.P2ChatAccessEventBotP2pChatEnteredV1Handler() {
                    @Override
                    public void handle(P2ChatAccessEventBotP2pChatEnteredV1 event) throws Exception {
                        // https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-access_event/events/bot_p2p_chat_entered
                        log.info("[用户进入机器人单聊事件], data: {}", Jsons.DEFAULT.toJson(event.getEvent()));

                        String openId = event.getEvent().getOperatorId().getOpenId();
                        userMap.computeIfAbsent(openId, k -> {
                            // 创建请求对象
                            GetUserReq req = GetUserReq.newBuilder()
                                    .userId(k)
                                    .userIdType("open_id")
                                    .build();

                            // 发起请求
                            try {
                                GetUserResp resp = client.contact().v3().user().get(req);
                                return resp.getData().getUser().getName();
                            } catch (Exception e) {
                                return "NULL";
                            }
                        });
                    }
                })
                .onP2BotMenuV6(new ApplicationService.P2BotMenuV6Handler() {
                    @Override
                    public void handle(P2BotMenuV6 event) throws Exception {
                        System.out.printf("[ 用户点击机器人菜单事件 ], data: %s\n", Jsons.DEFAULT.toJson(event.getEvent()));
                        /*
                         * 通过菜单 event_key 区分不同菜单。 你可以在开发者后台配置菜单的event_key
                         */
                        if ("send_alarm".equals(event.getEvent().getEventKey())) {
                            String openID = event.getEvent().getOperator().getOperatorId().getOpenId();
                            SecurityApplyCard.Params params = SecurityApplyCard.Params.build("测试内容", Arrays.asList("账号1", "账号2", "账号3", "账号4", "账号5", "账号6"), UUID.randomUUID().toString());
                            sendSecurityApplyCard(params);
                        }
                    }
                })
                .onP2CardActionTrigger(new P2CardActionTriggerHandler() {
                    @Override
                    public P2CardActionTriggerResponse handle(P2CardActionTrigger event) throws Exception {
                        // 处理卡片按钮点击回调
                        // https://open.feishu.cn/document/uAjLw4CM/ukzMukzMukzM/feishu-cards/card-callback-communication

                        SecurityApplyCard applyCard = SecurityApplyCard.createWith(event);
                        // 审核操作执行
                        publisher.publishEvent(new SecurityAuthorizeEventEvent(applyCard));

                        // 响应回调请求
                        P2CardActionTriggerResponse resp = new P2CardActionTriggerResponse();
                        CallBackToast toast = applyCard.getToast();
                        CallBackCard card = new CallBackCard();

                        // 卡片模板参数
                        Map<String, Object> data = new HashMap<>();
                        data.put("template_id", safeboxProperties.getLarkBot().getSecurityApplyCardId());
                        data.put("template_variable", applyCard.getTemplateParams().toMap());

                        // 卡片类型。可选值：template（卡片模板）、raw（卡片 JSON）。
                        card.setType("template");
                        card.setData(data);

                        resp.setToast(toast);
                        resp.setCard(card);
                        return resp;
                    }
                }).build();
    }

    /**
     * 发送密码申请卡片
     */
    public boolean sendSecurityApplyCard(SecurityApplyCard.Params params) throws Exception {
        // 卡片参数
        Map<String, Object> templateParams = params.toMap();
        return sendCard(safeboxProperties.getLarkBot().getSecurityApplyCardId(), templateParams, safeboxProperties.getLarkBot().getProcessReceiveId());
    }

    public boolean sendPasswordExpirationCard(Integer daysPast) throws Exception {
        // 卡片参数
        Map<String, Object> templateParams = new HashMap<>();
        templateParams.put("daysPast", daysPast);

        return sendCard(safeboxProperties.getLarkBot().getPasswordExpirationCardId(), templateParams, safeboxProperties.getLarkBot().getProcessReceiveId());
    }

    /**
     * 发送卡片
     * - 构造交互卡片
     * https://open.feishu.cn/document/uAjLw4CM/ukzMukzMukzM/feishu-cards/send-feishu-card#718fe26b
     * - 使用发送OpenAPI发送卡片
     * https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/create
     *
     * @param templateId     卡片ID
     * @param templateParams 卡片参数
     * @param receiveId      接收人(openID)
     * @return boolean 发送结果
     **/
    private boolean sendCard(String templateId, Map<String, Object> templateParams, String receiveId) throws Exception {
        // 构造卡片
        String replyContent = new MessageTemplate.Builder()
                .data(new MessageTemplateData.Builder().templateId(templateId)
                        .templateVariable(templateParams)
                        .build())
                .build();

        // 使用发送OpenAPI发送卡片
        CreateMessageResp resp = client.im().v1().message().create(
                CreateMessageReq.newBuilder()
                        .receiveIdType("open_id")
                        .createMessageReqBody(CreateMessageReqBody.newBuilder()
                                .receiveId(receiveId)
                                .msgType("interactive")
                                .content(replyContent)
                                .build())
                        .build()
        );

        // 处理服务端错误
        if (!resp.success()) {
            log.error("飞书服务端错误! code: {}, msg: {}, reqId: {}, resp: {}",
                    resp.getCode(), resp.getMsg(), resp.getRequestId(), Jsons.createGSON(true, false).toJson(JsonParser.parseString(new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8))));
            return false;
        }

        return true;
    }

}
